#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المحسن للأخبار مع دعم اللغات المتعددة
"""

import asyncio
import sys
import os

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__)))

async def test_enhanced_news_system():
    """اختبار شامل للنظام المحسن"""
    
    print("🧪 اختبار النظام المحسن للأخبار مع دعم اللغات المتعددة")
    print("=" * 70)
    
    try:
        # تهيئة قاعدة البيانات
        print("\n1️⃣ تهيئة قاعدة البيانات...")
        from integrations.firebase_init import initialize_firebase
        db = initialize_firebase()
        
        if db:
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
        else:
            print("❌ فشل في تهيئة قاعدة البيانات")
            return
        
        # تهيئة نظام الإشعارات
        print("\n2️⃣ تهيئة نظام الإشعارات...")
        from services.automatic_news_notifications import AutomaticNewsNotifications
        notifications = AutomaticNewsNotifications(db=db, bot=None)
        print("✅ تم تهيئة نظام الإشعارات")
        
        # اختبار قراءة لغة مستخدم حقيقي
        print("\n3️⃣ اختبار قراءة اللغات من قاعدة البيانات...")
        
        # الحصول على قائمة المستخدمين
        users_docs = db.collection('users').limit(5).get()
        test_users = []
        
        for doc in users_docs:
            if not doc.id.startswith('_'):
                test_users.append(doc.id)
        
        if test_users:
            print(f"📋 تم العثور على {len(test_users)} مستخدم للاختبار")
            
            for user_id in test_users[:3]:  # اختبار أول 3 مستخدمين
                lang = await notifications._get_user_language(user_id)
                print(f"  👤 المستخدم {user_id}: اللغة = {lang}")
        else:
            print("⚠️ لم يتم العثور على مستخدمين للاختبار")
        
        # اختبار إحصائيات اللغات
        print("\n4️⃣ اختبار إحصائيات اللغات...")
        stats = await notifications.get_language_usage_stats()
        
        if stats:
            print(f"📊 إجمالي المستخدمين: {stats.get('total_users', 0)}")
            print("🌍 توزيع اللغات:")
            for lang, count in stats.get('by_language', {}).items():
                lang_name = "العربية" if lang == 'ar' else "English"
                print(f"  • {lang_name}: {count} مستخدم")
        else:
            print("⚠️ لا توجد إحصائيات متاحة")
        
        # اختبار تقرير اللغات
        print("\n5️⃣ إنشاء تقرير اللغات...")
        report = await notifications.generate_language_report()
        print("📄 التقرير:")
        print("-" * 50)
        print(report)
        print("-" * 50)
        
        # اختبار إنشاء إعدادات افتراضية لمستخدم جديد
        print("\n6️⃣ اختبار إنشاء إعدادات افتراضية...")
        test_user_id = "test_user_12345"
        
        # إنشاء إعدادات افتراضية
        await notifications._create_default_user_settings(test_user_id)
        
        # قراءة الإعدادات المنشأة
        lang = await notifications._get_user_language(test_user_id)
        prefs = await notifications._get_user_notification_preferences(test_user_id)
        
        print(f"✅ تم إنشاء إعدادات للمستخدم {test_user_id}")
        print(f"  📝 اللغة: {lang}")
        print(f"  🔔 الإشعارات مفعلة: {prefs.get('enabled', False)}")
        print(f"  📋 أنواع الإشعارات: {len(prefs.get('types', []))}")
        
        # تنظيف البيانات التجريبية
        try:
            db.collection('user_settings').document(test_user_id).delete()
            db.collection('notification_preferences').document(test_user_id).delete()
            print("🧹 تم تنظيف البيانات التجريبية")
        except:
            pass
        
        print("\n🎉 تم اكتمال جميع الاختبارات بنجاح!")
        print("\n📋 ملخص النتائج:")
        print("✅ قراءة اللغات من قاعدة البيانات")
        print("✅ إنشاء إعدادات افتراضية")
        print("✅ إحصائيات اللغات")
        print("✅ تقارير مفصلة")
        print("✅ تفضيلات الإشعارات")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_enhanced_news_system())
