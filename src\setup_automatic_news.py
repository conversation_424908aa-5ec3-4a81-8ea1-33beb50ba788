#!/usr/bin/env python3
"""
إعداد سريع لنظام الأخبار التلقائي
"""

import asyncio
import logging
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def setup_automatic_news_system():
    """إعداد نظام الأخبار التلقائي"""
    print("🚀 إعداد نظام الأخبار التلقائي...")
    print("=" * 60)
    
    try:
        # الخطوة 1: تهيئة قاعدة البيانات (إذا كانت متوفرة)
        print("\n📊 تهيئة قاعدة البيانات...")
        db = None
        try:
            from integrations.firebase_init import db as firebase_db
            db = firebase_db
            if db:
                print("✅ تم الاتصال بقاعدة البيانات Firebase")
            else:
                print("⚠️ قاعدة البيانات غير متوفرة - سيتم العمل بدون قاعدة بيانات")
        except Exception as db_error:
            print(f"⚠️ لم يتم الاتصال بقاعدة البيانات: {str(db_error)}")
        
        # الخطوة 2: تهيئة البوت (إذا كان متوفراً)
        print("\n🤖 تهيئة البوت...")
        bot = None
        try:
            from core.telegram_bot import TelegramBot
            bot = TelegramBot()
            await bot.setup()
            print("✅ تم إعداد البوت بنجاح")
        except Exception as bot_error:
            print(f"⚠️ لم يتم إعداد البوت: {str(bot_error)}")
        
        # الخطوة 3: تهيئة نظام التكامل التلقائي
        print("\n🔗 تهيئة نظام التكامل التلقائي...")
        from services.automatic_news_integration import initialize_automatic_news_integration
        
        integration = await initialize_automatic_news_integration(db, bot)
        
        if integration:
            print("✅ تم تهيئة نظام التكامل بنجاح")
            
            # عرض حالة التكامل
            status = integration.get_integration_status()
            print(f"\n📊 حالة التكامل:")
            print(f"  - مُهيأ: {status['initialized']}")
            print(f"  - يعمل: {status['running']}")
            print(f"  - الأنظمة:")
            for system, available in status['systems'].items():
                status_icon = "✅" if available else "❌"
                print(f"    {status_icon} {system}")
            
            # الخطوة 4: بدء تشغيل الأنظمة التلقائية
            print("\n🚀 بدء تشغيل الأنظمة التلقائية...")
            start_success = await integration.start_automatic_systems()
            
            if start_success:
                print("✅ تم بدء تشغيل جميع الأنظمة التلقائية بنجاح!")
                
                # عرض صحة النظام
                health = await integration.get_system_health()
                print(f"\n🏥 صحة النظام: {health['overall_status']}")
                
                # عرض تفاصيل الأنظمة
                print(f"\n📋 تفاصيل الأنظمة:")
                for system_name, system_data in health['systems'].items():
                    status = system_data.get('status', 'unknown')
                    print(f"  • {system_name}: {status}")
                
                # إرشادات للمستخدم
                print(f"\n" + "=" * 60)
                print("🎉 تم إعداد نظام الأخبار التلقائي بنجاح!")
                print("\n📋 الخطوات التالية:")
                print("1. النظام يعمل الآن تلقائياً في الخلفية")
                print("2. سيتم جلب الأخبار حسب الجدولة المحددة:")
                print("   - الصباح (6-12): كل 10 دقائق")
                print("   - بعد الظهر (12-18): كل 15 دقيقة")
                print("   - المساء (18-24): كل 30 دقيقة")
                print("   - الليل (0-6): كل ساعة")
                print("3. يمكن للمستخدمين إدارة التنبيهات من قائمة الأخبار")
                print("4. يمكن للمطور مراقبة النظام عبر أمر /dashboard")
                
                # حفظ معلومات النظام
                system_info = {
                    'setup_time': str(asyncio.get_event_loop().time()),
                    'integration_status': status,
                    'system_health': health
                }
                
                print(f"\n💾 معلومات النظام محفوظة")
                
                return integration
                
            else:
                print("❌ فشل في بدء تشغيل الأنظمة التلقائية")
                return None
        else:
            print("❌ فشل في تهيئة نظام التكامل")
            return None
            
    except Exception as e:
        print(f"\n❌ خطأ في إعداد النظام: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return None

async def verify_system_setup():
    """التحقق من إعداد النظام"""
    print("\n🔍 التحقق من إعداد النظام...")
    
    try:
        # التحقق من الملفات المطلوبة
        required_files = [
            'src/services/smart_rate_limiter.py',
            'src/services/automatic_news_scheduler.py',
            'src/services/intelligent_news_cache.py',
            'src/services/automatic_news_notifications.py',
            'src/handlers/api_monitoring_dashboard.py',
            'src/services/automatic_news_integration.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            print("❌ ملفات مفقودة:")
            for file_path in missing_files:
                print(f"  - {file_path}")
            return False
        else:
            print("✅ جميع الملفات المطلوبة موجودة")
        
        # التحقق من إمكانية الاستيراد
        try:
            from services.smart_rate_limiter import SmartRateLimiter
            from services.automatic_news_scheduler import AutomaticNewsScheduler
            from services.intelligent_news_cache import IntelligentNewsCache
            from services.automatic_news_notifications import AutomaticNewsNotifications
            from handlers.api_monitoring_dashboard import APIMonitoringDashboard
            from services.automatic_news_integration import AutomaticNewsIntegration
            print("✅ جميع الوحدات قابلة للاستيراد")
        except Exception as import_error:
            print(f"❌ خطأ في استيراد الوحدات: {str(import_error)}")
            return False
        
        # التحقق من المتطلبات
        try:
            import apscheduler
            import pytz
            print("✅ جميع المتطلبات متوفرة")
        except ImportError as req_error:
            print(f"❌ متطلبات مفقودة: {str(req_error)}")
            print("💡 قم بتشغيل: pip install apscheduler pytz")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🔧 مُعِد نظام الأخبار التلقائي")
    print("=" * 60)
    
    # التحقق من الإعداد أولاً
    if not await verify_system_setup():
        print("\n❌ فشل في التحقق من الإعداد")
        return
    
    # إعداد النظام
    integration = await setup_automatic_news_system()
    
    if integration:
        print(f"\n✅ تم إعداد النظام بنجاح!")
        print("🎯 النظام جاهز للاستخدام")
        
        # اختبار سريع
        print(f"\n🧪 اختبار سريع...")
        try:
            # اختبار معالجة الأخبار التلقائية
            await integration.process_news_automatically()
            print("✅ اختبار المعالجة التلقائية نجح")
        except Exception as test_error:
            print(f"⚠️ تحذير في الاختبار: {str(test_error)}")
        
        # إيقاف النظام بعد الإعداد (سيتم تشغيله مع البوت الرئيسي)
        await integration.stop_automatic_systems()
        print("✅ تم إيقاف النظام مؤقتاً (سيتم تشغيله مع البوت الرئيسي)")
        
    else:
        print(f"\n❌ فشل في إعداد النظام")

if __name__ == "__main__":
    asyncio.run(main())
