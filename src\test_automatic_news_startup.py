#!/usr/bin/env python3
"""
اختبار بدء تشغيل نظام الأخبار التلقائي
"""

import asyncio
import logging
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_automatic_news_startup():
    """اختبار بدء تشغيل نظام الأخبار التلقائي"""
    print("🧪 اختبار بدء تشغيل نظام الأخبار التلقائي...")
    print("=" * 60)
    
    try:
        # اختبار 1: تهيئة نظام إدارة معدل الطلبات
        print("\n📊 اختبار نظام إدارة معدل الطلبات...")
        try:
            from services.smart_rate_limiter import initialize_smart_rate_limiter
            rate_limiter = initialize_smart_rate_limiter()
            
            # اختبار الوظائف الأساسية
            can_request, reason = await rate_limiter.can_make_request('binance')
            print(f"✅ نظام إدارة معدل الطلبات: {can_request} - {reason}")
            
        except Exception as e:
            print(f"❌ خطأ في نظام إدارة معدل الطلبات: {str(e)}")
        
        # اختبار 2: تهيئة نظام التخزين المؤقت
        print("\n💾 اختبار نظام التخزين المؤقت...")
        try:
            from services.intelligent_news_cache import initialize_intelligent_news_cache
            cache = initialize_intelligent_news_cache(max_size_mb=10)
            
            # اختبار الوظائف الأساسية
            await cache.set("test", {"data": "test"}, source="test")
            data = await cache.get("test")
            print(f"✅ نظام التخزين المؤقت: البيانات = {data}")
            
        except Exception as e:
            print(f"❌ خطأ في نظام التخزين المؤقت: {str(e)}")
        
        # اختبار 3: تهيئة نظام الجدولة
        print("\n⏰ اختبار نظام الجدولة...")
        try:
            from services.automatic_news_scheduler import initialize_automatic_news_scheduler
            scheduler = initialize_automatic_news_scheduler()
            
            # اختبار الوظائف الأساسية
            status = scheduler.get_scheduler_status()
            print(f"✅ نظام الجدولة: الحالة = {status['status']}")
            
        except Exception as e:
            print(f"❌ خطأ في نظام الجدولة: {str(e)}")
        
        # اختبار 4: تهيئة نظام الإشعارات
        print("\n🔔 اختبار نظام الإشعارات...")
        try:
            from services.automatic_news_notifications import initialize_automatic_news_notifications
            notifications = initialize_automatic_news_notifications()
            
            # اختبار الوظائف الأساسية
            stats = await notifications.get_notification_stats()
            print(f"✅ نظام الإشعارات: المستخدمون النشطون = {stats['active_users']}")
            
        except Exception as e:
            print(f"❌ خطأ في نظام الإشعارات: {str(e)}")
        
        # اختبار 5: تهيئة لوحة التحكم
        print("\n🖥️ اختبار لوحة التحكم...")
        try:
            from handlers.api_monitoring_dashboard import initialize_api_monitoring_dashboard
            dashboard = initialize_api_monitoring_dashboard()
            
            print("✅ لوحة التحكم: تم التهيئة بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في لوحة التحكم: {str(e)}")
        
        # اختبار 6: تهيئة نظام التكامل الشامل
        print("\n🔗 اختبار نظام التكامل الشامل...")
        try:
            from services.automatic_news_integration import initialize_automatic_news_integration
            integration = await initialize_automatic_news_integration()
            
            if integration:
                status = integration.get_integration_status()
                print(f"✅ نظام التكامل: مُهيأ = {status['initialized']}")
                
                # اختبار صحة النظام
                health = await integration.get_system_health()
                print(f"✅ صحة النظام: {health['overall_status']}")
                
                # إيقاف النظام بعد الاختبار
                await integration.stop_automatic_systems()
                print("✅ تم إيقاف النظام بعد الاختبار")
                
            else:
                print("❌ فشل في تهيئة نظام التكامل")
                
        except Exception as e:
            print(f"❌ خطأ في نظام التكامل: {str(e)}")
        
        print("\n" + "=" * 60)
        print("🎉 انتهى اختبار بدء التشغيل!")
        print("✅ جميع الأنظمة جاهزة للتشغيل")
        
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {str(e)}")
        import traceback
        print(traceback.format_exc())

async def test_integration_with_mock_db():
    """اختبار التكامل مع قاعدة بيانات وهمية"""
    print("\n🧪 اختبار التكامل مع قاعدة بيانات وهمية...")
    
    # إنشاء قاعدة بيانات وهمية
    class MockDB:
        def __init__(self):
            self.collections = {}
        
        def collection(self, name):
            if name not in self.collections:
                self.collections[name] = MockCollection()
            return self.collections[name]
    
    class MockCollection:
        def __init__(self):
            self.documents = {}
        
        def document(self, doc_id):
            return MockDocument(doc_id, self.documents)
        
        def where(self, field, op, value):
            return MockQuery()
        
        def order_by(self, field, direction=None):
            return MockQuery()
        
        def limit(self, count):
            return MockQuery()
    
    class MockDocument:
        def __init__(self, doc_id, documents):
            self.doc_id = doc_id
            self.documents = documents
        
        def set(self, data, merge=False):
            self.documents[self.doc_id] = data
        
        def get(self):
            return MockDocumentSnapshot(self.documents.get(self.doc_id))
        
        def update(self, data):
            if self.doc_id in self.documents:
                self.documents[self.doc_id].update(data)
        
        def delete(self):
            if self.doc_id in self.documents:
                del self.documents[self.doc_id]
    
    class MockDocumentSnapshot:
        def __init__(self, data):
            self.data = data
        
        @property
        def exists(self):
            return self.data is not None
        
        def to_dict(self):
            return self.data or {}
    
    class MockQuery:
        def get(self):
            return []
        
        def where(self, field, op, value):
            return self
        
        def order_by(self, field, direction=None):
            return self
        
        def limit(self, count):
            return self
    
    # إنشاء bot وهمي
    class MockBot:
        async def send_message(self, chat_id, text, **kwargs):
            print(f"📱 رسالة وهمية إلى {chat_id}: {text[:50]}...")
            return True
    
    try:
        mock_db = MockDB()
        mock_bot = MockBot()
        
        # اختبار التكامل
        from services.automatic_news_integration import initialize_automatic_news_integration
        integration = await initialize_automatic_news_integration(mock_db, mock_bot)
        
        if integration:
            print("✅ تم تهيئة النظام مع قاعدة البيانات الوهمية")
            
            # اختبار بدء التشغيل
            start_success = await integration.start_automatic_systems()
            print(f"✅ بدء التشغيل: {start_success}")
            
            # اختبار صحة النظام
            health = await integration.get_system_health()
            print(f"✅ صحة النظام: {health['overall_status']}")
            
            # إيقاف النظام
            await integration.stop_automatic_systems()
            print("✅ تم إيقاف النظام")
            
        else:
            print("❌ فشل في تهيئة النظام مع قاعدة البيانات الوهمية")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")

async def main():
    """الدالة الرئيسية للاختبار"""
    await test_automatic_news_startup()
    await test_integration_with_mock_db()

if __name__ == "__main__":
    asyncio.run(main())
