# نظام الأخبار التلقائي الذكي

## نظرة عامة

تم تطوير نظام الأخبار التلقائي الذكي ليحل محل النظام اليدوي السابق، ويوفر تجربة مستخدم محسنة مع إدارة ذكية لموارد API والتنبيهات التلقائية.

## المكونات الرئيسية

### 1. نظام إدارة معدل الطلبات الذكي (Smart Rate Limiter)
**الملف:** `src/services/smart_rate_limiter.py`

**الوظائف:**
- إدارة ذكية لحدود API لكل منصة
- توزيع الطلبات بكفاءة عبر اليوم
- مراقبة الأداء وتحليل الاستخدام
- تحسين توزيع الطلبات تلقائياً

**حدود API المدعومة:**
- **Binance**: 60 طلب/دقيقة
- **CoinDesk**: 30 طلب/دقيقة  
- **CoinGecko**: 50 طلب/دقيقة
- **Gemini AI (2.0-flash)**: 60 طلب/دقيقة

### 2. نظام التخزين المؤقت الذكي (Intelligent Cache)
**الملف:** `src/services/intelligent_news_cache.py`

**الوظائف:**
- تخزين مؤقت ذكي للأخبار
- إدارة الذاكرة التلقائية
- فهرسة متقدمة للبحث السريع
- تنظيف تلقائي للبيانات القديمة
- تقييم صحة التخزين المؤقت

### 3. نظام الجدولة التلقائية (Automatic Scheduler)
**الملف:** `src/services/automatic_news_scheduler.py`

**الوظائف:**
- جدولة ذكية لجلب الأخبار
- توزيع الطلبات حسب أوقات اليوم
- إدارة المشتركين
- مراقبة الأداء

**الجدولة المثلى:**
- **الصباح (6-12)**: كل 10 دقائق - أولوية عالية
- **بعد الظهر (12-18)**: كل 15 دقيقة - أولوية متوسطة
- **المساء (18-24)**: كل 30 دقيقة - أولوية منخفضة
- **الليل (0-6)**: كل ساعة - أولوية أدنى

### 4. نظام الإشعارات التلقائية (Automatic Notifications)
**الملف:** `src/services/automatic_news_notifications.py`

**الوظائف:**
- إشعارات تلقائية للأخبار المهمة لجميع المستخدمين
- تصنيف ذكي للأخبار
- إدارة قواعد المستخدمين
- تجنب الإزعاج مع حدود الإرسال
- **تفعيل تلقائي للإشعارات لجميع المستخدمين (محدث)**

**أنواع الإشعارات:**
- 🚨 الأخبار العاجلة
- 🆕 العملات الجديدة
- 📊 تحليلات السوق
- 📋 الملخص اليومي

### 5. لوحة تحكم مراقبة API (API Monitoring Dashboard)
**الملف:** `src/handlers/api_monitoring_dashboard.py`

**الوظائف:**
- مراقبة استخدام API في الوقت الفعلي
- إحصائيات مفصلة لكل منصة
- تقييم صحة النظام
- توصيات التحسين

### 6. نظام التكامل الشامل (Integration System)
**الملف:** `src/services/automatic_news_integration.py`

**الوظائف:**
- ربط جميع الأنظمة الفرعية
- إدارة دورة حياة النظام
- مراقبة الصحة الشاملة
- معالجة الأخبار التلقائية

## واجهة المستخدم المحدثة

### القائمة الرئيسية للأخبار
- **للمشتركين:**
  - 📊 آخر الأخبار
  - 🔥 الأخبار العاجلة
  - 🆕 العملات الجديدة
  - 🔔 إدارة التنبيهات
  - 📈 إحصائيات النظام

- **للمستخدمين المجانيين:**
  - 📊 الأخبار العامة (مجاني)
  - 💎 الاشتراك للنظام التلقائي

### إعدادات التنبيهات
يمكن للمستخدمين تفعيل/إلغاء التنبيهات لكل نوع:
- الأخبار العاجلة
- العملات الجديدة
- تحليلات السوق
- الملخص اليومي

## التثبيت والتشغيل

### 1. تثبيت المتطلبات الإضافية
```bash
pip install apscheduler pytz
```

### 2. تهيئة النظام
```python
from services.automatic_news_integration import initialize_automatic_news_integration

# تهيئة النظام
integration = await initialize_automatic_news_integration(db, bot)

# بدء التشغيل
if integration:
    await integration.start_automatic_systems()
```

### 3. إضافة معالجات الأحداث
```python
# في main.py
from handlers.news_handlers import handle_news_callback
from handlers.api_monitoring_dashboard import handle_dashboard_callback

# إضافة المعالجات للبوت
application.add_handler(CallbackQueryHandler(handle_news_callback, pattern="^news_"))
application.add_handler(CallbackQueryHandler(handle_dashboard_callback, pattern="^dashboard_"))
```

## الاختبار

### تشغيل الاختبارات الشاملة
```bash
python src/test/test_automatic_news_system.py
```

### اختبارات فردية
```python
# اختبار نظام إدارة معدل الطلبات
from services.smart_rate_limiter import SmartRateLimiter
rate_limiter = SmartRateLimiter()
can_request, reason = await rate_limiter.can_make_request('binance')

# اختبار التخزين المؤقت
from services.intelligent_news_cache import IntelligentNewsCache
cache = IntelligentNewsCache()
await cache.set("test", {"data": "test"})
data = await cache.get("test")
```

## المراقبة والصيانة

### 1. مراقبة صحة النظام
```python
health = await integration.get_system_health()
print(f"حالة النظام: {health['overall_status']}")
```

### 2. إحصائيات الاستخدام
```python
# إحصائيات API
stats = await rate_limiter.get_usage_analytics('binance', days=7)

# إحصائيات التخزين المؤقت
cache_stats = await cache.get_cache_stats()

# إحصائيات الإشعارات
notif_stats = await notifications.get_notification_stats()
```

### 3. لوحة التحكم
- الوصول عبر: `/dashboard` (للمطور فقط)
- مراقبة جميع الأنظمة في مكان واحد
- إحصائيات مفصلة وتوصيات التحسين

## الأمان والأداء

### الأمان
- تشفير مفاتيح API
- التحقق من صلاحيات المستخدم
- حماية من تجاوز حدود API
- تسجيل شامل للأنشطة

### الأداء
- تخزين مؤقت ذكي يقلل طلبات API
- توزيع الطلبات لتجنب الازدحام
- معالجة غير متزامنة
- تنظيف تلقائي للذاكرة

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **تجاوز حدود API**
   - الحل: النظام يدير هذا تلقائياً
   - المراقبة: لوحة التحكم تعرض الاستخدام الحالي

2. **بطء في الاستجابة**
   - الحل: تحسين إعدادات التخزين المؤقت
   - المراقبة: إحصائيات وقت الاستجابة

3. **عدم وصول الإشعارات**
   - التحقق من إعدادات المستخدم
   - مراجعة سجلات النظام

### السجلات
```python
import logging
logging.basicConfig(level=logging.INFO)

# سجلات مفصلة لكل نظام
logger = logging.getLogger('automatic_news')
```

## التطوير المستقبلي

### ميزات مخططة
- دعم منصات أخبار إضافية
- تحليل المشاعر المتقدم
- توقعات الأسعار بالذكاء الاصطناعي
- تخصيص أكثر للتنبيهات

### المساهمة
- اتبع معايير الكود الموجودة
- أضف اختبارات للميزات الجديدة
- حدث الوثائق عند الحاجة

## 🆕 التحديثات الجديدة

### الإشعارات التلقائية لجميع المستخدمين
**التاريخ:** 2025-06-27

**التحسينات:**
- ✅ **إشعارات شاملة**: جميع المستخدمين يحصلون على الأخبار العاجلة والعملات الجديدة تلقائياً
- ✅ **لا حاجة للاشتراك**: النظام يعمل لجميع المستخدمين بغض النظر عن حالة الاشتراك
- ✅ **تفعيل تلقائي**: المستخدمون الجدد يحصلون على الإشعارات فور التسجيل
- ✅ **حدود ذكية**: حماية من الإزعاج مع حدود يومية مناسبة

**الحدود اليومية:**
- **الأخبار العاجلة**: 5 إشعارات يومياً كحد أقصى
- **العملات الجديدة**: 3 إشعارات يومياً كحد أقصى

**كيفية العمل:**
1. عند بدء النظام، يتم تفعيل الإشعارات لجميع المستخدمين الحاليين
2. المستخدمون الجدد يحصلون على الإشعارات تلقائياً عند التسجيل
3. النظام يرسل الأخبار العاجلة والعملات الجديدة لجميع المستخدمين النشطين
4. المستخدمون المحظورون لا يحصلون على إشعارات

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- راجع السجلات أولاً
- استخدم لوحة التحكم للتشخيص
- تحقق من صحة النظام

---

**تاريخ آخر تحديث:** 2025-01-26  
**الإصدار:** 2.0.0  
**المطور:** نظام الأخبار التلقائي الذكي
