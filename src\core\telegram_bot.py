"""
كلاس إدارة البوت وتنظيم دورة حياته
"""

import logging
import asyncio
from datetime import time as datetime_time
from telegram import Update
from telegram.ext import (
    <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Message<PERSON><PERSON><PERSON>, 
    CallbackQuery<PERSON><PERSON>ler, PollAnswerHandler, filters, CallbackContext
)
from telegram.request import HTTPXRequest

# استيراد التكوين
from config import Config

# استيراد الخدمات المطلوبة
from services.system_settings import system_settings
from services.subscription_system import SubscriptionSystem
from services.free_day_system import free_day_system
from services.automatic_payment_verification import AutomaticPaymentVerifier
from services.error_handler import telegram_error_handler
from services.alert_service import check_alerts, send_daily_report
from services.backup_service import perform_backup
from services import (
    cleanup_pending_transactions,
    notify_expiring_transactions,
    initialize_transaction_manager
)

# استيراد معالجات الأوامر
from services.user_management import (
    start, show_user_stats, check_expired_subscriptions,
    notify_expiring_subscriptions
)
from services.admin_service import (
    cast, ban_user, unban_user, system_info,
    cleanup_system, grant_free_day_command, stop_all_scheduled_tasks
)
from services.api_management import (
    api_setup_command, api_info_command, delete_api_command
)
from handlers.main_handlers import (
    help_command, button_click, handle_message,
    handle_trading_education_callback, handle_ai_tutor_message_wrapper
)
from services.alert_service import alert_command

# استيراد دالة التحليل
from analysis.enhanced_analysis import analyze_symbol_enhanced as analyze_command

# استيراد وحدة تعليم التداول
from education.trading_education import (
    handle_learn_trading_ai, handle_quiz_answer
)

# استيراد التحليل المحسن
from analysis.enhanced_analysis import (
    compare_trading_styles, refresh_analysis
)

# استيراد دوال التهيئة
from core.system_initialization import initialize_system

# استيراد مدير API
from api_manager import APIManager

# إعداد السجلات
logger = logging.getLogger(__name__)

class TelegramBot:
    """فئة لإدارة البوت وتنظيم دورة حياته"""
    
    def __init__(self):
        self.application = None
        self.config = Config()
        self._running = False

    async def setup(self):
        """إعداد البوت وتهيئة المكونات"""
        # تعطيل سجلات غير ضرورية
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("apscheduler").setLevel(logging.WARNING)

        # إنشاء التطبيق مع إعدادات محسنة للاتصال
        request = HTTPXRequest(
            connection_pool_size=16,    # زيادة حجم مجموعة الاتصالات
            connect_timeout=30.0,       # مهلة الاتصال 30 ثانية
            read_timeout=30.0,          # مهلة القراءة 30 ثانية
            write_timeout=30.0,         # مهلة الكتابة 30 ثانية
            pool_timeout=30.0           # مهلة انتظار الاتصال من المجموعة
        )

        # إنشاء التطبيق مع إعدادات محسنة
        self.application = (Application.builder()
                          .token(self.config.bot_token)
                          .request(request)
                          .concurrent_updates(True)  # تمكين التحديثات المتزامنة
                          .build())

        # تهيئة مدير المعاملات
        await initialize_transaction_manager()

        # تهيئة وحدة الدردشة مع الذكاء الاصطناعي
        try:
            import ai_chat
            # تعطيل تهيئة ai_chat مؤقتاً لأنه يحتاج api_manager مع db
            # سيتم تهيئته لاحقاً في النسخة الموسعة
            logger.info("ℹ️ تم تأجيل تهيئة وحدة الدردشة مع الذكاء الاصطناعي")
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة وحدة الدردشة مع الذكاء الاصطناعي: {str(e)}")

        logger.info("✅ تم تهيئة البوت الأساسي بنجاح")



    async def setup_handlers_after_system_init(self):
        """إعداد معالجات الأوامر بعد تهيئة النظام"""
        logger.info("🔧 جاري إعداد معالجات الأوامر...")

        # إضافة معالجات الأوامر
        await self._setup_handlers()

        # جدولة المهام
        await self._setup_scheduled_jobs()

        logger.info("✅ تم تهيئة معالجات البوت بنجاح")
        return True

    async def _setup_handlers(self):
        """إعداد معالجات الأوامر والرسائل"""

        # معالجات الأوامر الأساسية
        self.application.add_handler(CommandHandler("start", start))
        self.application.add_handler(CommandHandler("help", help_command))
        self.application.add_handler(CommandHandler("alert", alert_command))
        self.application.add_handler(CommandHandler("analyze", analyze_command))

        # أوامر إدارة الاشتراكات
        from services.subscription_system import get_subscription_system
        subscription_system = get_subscription_system()
        if subscription_system:
            self.application.add_handler(CommandHandler("test_subscription", subscription_system.test_subscription))
        self.application.add_handler(CommandHandler("stats", show_user_stats))

        # أوامر إدارة API
        self.application.add_handler(CommandHandler("setup_api", api_setup_command))
        self.application.add_handler(CommandHandler("api_info", api_info_command))
        self.application.add_handler(CommandHandler("delete_api", delete_api_command))

        # أوامر إدارة النظام
        self.application.add_handler(CommandHandler("stop_tasks", stop_all_scheduled_tasks))
        self.application.add_handler(CommandHandler("backup", perform_backup))
        self.application.add_handler(CommandHandler("system_info", system_info))
        self.application.add_handler(CommandHandler("cleanup", cleanup_system))
        self.application.add_handler(CommandHandler("free_day", grant_free_day_command))

        # أوامر نظام الأخبار التلقائي (للمطور فقط)
        try:
            from handlers.api_monitoring_dashboard import api_monitoring_dashboard
            if api_monitoring_dashboard:
                self.application.add_handler(CommandHandler("dashboard", api_monitoring_dashboard.show_main_dashboard))
                logger.info("✅ تم إضافة أمر لوحة تحكم مراقبة API")
        except Exception as dashboard_cmd_error:
            logger.warning(f"⚠️ لم يتم إضافة أمر لوحة التحكم: {str(dashboard_cmd_error)}")

        # أوامر المشرف
        self.application.add_handler(CommandHandler("cast", cast))
        self.application.add_handler(CommandHandler("ban", ban_user))
        self.application.add_handler(CommandHandler("unban", unban_user))

        # معالجات محددة للـ callback queries (يجب أن تكون قبل المعالج العام)

        # معالجات الشروط والأحكام واللغة الأولية
        self.application.add_handler(CallbackQueryHandler(
            button_click,
            pattern='^(terms_agree|terms_decline|set_initial_lang_ar|set_initial_lang_en)$'
        ))

        # معالجات تعليم التداول بالذكاء الاصطناعي
        self.application.add_handler(CommandHandler("learn_trading_ai", handle_learn_trading_ai))
        self.application.add_handler(CallbackQueryHandler(
            handle_trading_education_callback,
            pattern='^(next_chapter_|start_quiz|ask_ai_tutor|add_gemini_key|learn_trading_ai|supplementary_chapters|supplementary_chapter_|back_to_quiz_results)'
        ))

        # معالجات API setup (يجب أن تكون قبل المعالج العام)
        self.application.add_handler(CallbackQueryHandler(
            button_click,
            pattern='^(setup_api_keys|select_platform|setup_.*_api|delete_api_keys|delete_.*_api)$'
        ))

        # معالجات التفاعل
        from services import extend_transaction, complete_payment, verify_payment
        self.application.add_handler(CallbackQueryHandler(extend_transaction, pattern=r"^extend_transaction_"))
        self.application.add_handler(CallbackQueryHandler(complete_payment, pattern=r"^complete_payment_"))
        self.application.add_handler(CallbackQueryHandler(verify_payment, pattern=r"^verify_payment_"))

        # معالجات النظام المحسن
        self.application.add_handler(CallbackQueryHandler(
            self._handle_enhanced_callbacks,
            pattern=r"^(compare_styles_|set_trading_style_|refresh_analysis_)"
        ))

        # معالجات نظام الأخبار التلقائي الجديد
        try:
            from handlers.news_handlers import handle_news_callback
            self.application.add_handler(CallbackQueryHandler(
                handle_news_callback,
                pattern=r"^(news_|notif_)"
            ))
            logger.info("✅ تم إضافة معالجات نظام الأخبار التلقائي")
        except Exception as news_handler_error:
            logger.warning(f"⚠️ لم يتم إضافة معالجات الأخبار التلقائية: {str(news_handler_error)}")

        # معالجات لوحة تحكم مراقبة API
        try:
            from handlers.api_monitoring_dashboard import handle_dashboard_callback
            self.application.add_handler(CallbackQueryHandler(
                handle_dashboard_callback,
                pattern=r"^(dashboard_|api_platform_|cache_|scheduler_|notif_enable_|notif_disable_)"
            ))
            logger.info("✅ تم إضافة معالجات لوحة تحكم مراقبة API")
        except Exception as dashboard_handler_error:
            logger.warning(f"⚠️ لم يتم إضافة معالجات لوحة التحكم: {str(dashboard_handler_error)}")

        # المعالج العام للأزرار (يجب أن يكون في النهاية)
        self.application.add_handler(CallbackQueryHandler(button_click))

        # معالجات الرسائل النصية (معالج واحد فقط لتجنب التضارب)
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))

        # معالج إجابات استطلاعات الرأي (للاختبارات)
        self.application.add_handler(PollAnswerHandler(handle_quiz_answer))

        # إضافة معالج الأخطاء
        self.application.add_error_handler(telegram_error_handler)

    async def _setup_scheduled_jobs(self):
        """إعداد المهام المجدولة"""
        job_queue = self.application.job_queue
        
        # المهام الأساسية
        job_queue.run_repeating(check_alerts, interval=300)
        job_queue.run_repeating(perform_backup, interval=86400)
        job_queue.run_repeating(cleanup_pending_transactions, interval=3600)
        job_queue.run_repeating(check_expired_subscriptions, interval=3600)
        job_queue.run_repeating(notify_expiring_subscriptions, interval=86400)
        job_queue.run_repeating(send_daily_report, interval=43200)
        job_queue.run_repeating(notify_expiring_transactions, interval=900)

        # مهمة تنظيف البيانات المؤقتة منتهية الصلاحية
        async def cleanup_expired_cache(context: CallbackContext):
            try:
                logger.info("🔄 بدء تنظيف البيانات المؤقتة منتهية الصلاحية...")
                from utils.firestore_cache import FirestoreCache
                from integrations.firebase_init import db
                firestore_cache = FirestoreCache(db)
                deleted_count = firestore_cache.clear_expired()
                logger.info(f"✅ تم حذف {deleted_count} من البيانات المؤقتة منتهية الصلاحية")
            except Exception as e:
                logger.error(f"❌ خطأ في تنظيف البيانات المؤقتة: {str(e)}")

        # تشغيل كل 6 ساعات مع تأخير أولي 5 دقائق
        job_queue.run_repeating(cleanup_expired_cache, interval=21600, first=300, name="cleanup_cache")

        # مهام نظام الأيام المجانية
        await self._setup_free_day_jobs(job_queue)

    async def _setup_free_day_jobs(self, job_queue):
        """إعداد مهام نظام الأيام المجانية"""
        # التحقق من وتحديث حالة الأيام المجانية
        async def check_free_days(context: CallbackContext):
            try:
                logger.info("🔄 جاري التحقق من وتحديث حالة الأيام المجانية...")
                activated, deactivated = await free_day_system.check_and_update_free_days()
                logger.info(f"✅ تم تفعيل اليوم المجاني لـ {activated} مستخدم وإلغاء تفعيل اليوم المجاني لـ {deactivated} مستخدم")
            except Exception as e:
                logger.error(f"❌ خطأ في التحقق من وتحديث حالة الأيام المجانية: {str(e)}")

        # إرسال تذكيرات اليوم المجاني
        async def send_free_day_reminders(context: CallbackContext):
            try:
                logger.info("🔄 جاري إرسال تذكيرات اليوم المجاني...")
                reminder_count = await free_day_system.send_free_day_reminders()
                logger.info(f"✅ تم إرسال تذكيرات اليوم المجاني لـ {reminder_count} مستخدم")
            except Exception as e:
                logger.error(f"❌ خطأ في إرسال تذكيرات اليوم المجاني: {str(e)}")

        # تنظيف إشعارات اليوم المجاني القديمة
        async def cleanup_free_day_notifications(context: CallbackContext):
            try:
                logger.info("🔄 جاري تنظيف إشعارات اليوم المجاني القديمة...")
                deleted_count = await free_day_system.cleanup_free_day_notifications(days_old=7)
                logger.info(f"✅ تم حذف {deleted_count} إشعار يوم مجاني قديم")
            except Exception as e:
                logger.error(f"❌ خطأ في تنظيف إشعارات اليوم المجاني القديمة: {str(e)}")

        # جدولة المهام مع أسماء واضحة
        job_queue.run_repeating(check_free_days, interval=3600, first=60, name="check_free_days")
        job_queue.run_daily(send_free_day_reminders, time=datetime_time(hour=9, minute=0, second=0), name="free_day_reminders")
        job_queue.run_daily(cleanup_free_day_notifications, time=datetime_time(hour=3, minute=0, second=0), name="cleanup_notifications")

    async def start(self):
        """بدء تشغيل البوت"""
        try:
            logger.info("🚀 جاري بدء تشغيل البوت...")

            # تهيئة التطبيق
            await self.application.initialize()
            await self.application.start()
            self._running = True

            # بدء الاستطلاع
            logger.info("🔄 جاري بدء استطلاع التحديثات...")
            await self.application.updater.start_polling(
                allowed_updates=Update.ALL_TYPES,
                drop_pending_updates=True,
                poll_interval=1.0,  # استطلاع كل ثانية
                timeout=10,         # مهلة زمنية 10 ثوان
                bootstrap_retries=5  # 5 محاولات إعادة الاتصال
            )
            logger.info("✅ تم بدء تشغيل البوت بنجاح")

            # إضافة مجدول للتحقق من الإعدادات كل 6 ساعات
            self.application.job_queue.run_repeating(
                self._check_settings_periodically,
                interval=21600,  # كل 6 ساعات
                first=21600      # بعد 6 ساعات من بدء التشغيل
            )

            logger.info("✅ البوت جاهز لاستقبال الرسائل")
            return

        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل البوت: {str(e)}")
            self._running = False
            raise
        finally:
            # لا نقوم بإيقاف البوت هنا لأننا نريده أن يستمر في العمل
            pass

    async def run_forever(self):
        """تشغيل البوت إلى أجل غير مسمى"""
        try:
            logger.info("🔄 البوت يعمل الآن بشكل مستمر...")

            # إضافة مراقبة دورية لحالة البوت
            async def monitor_bot_health():
                """مراقبة صحة البوت"""
                while self._running:
                    try:
                        # التحقق من حالة الاتصال
                        if not self.application.updater.running:
                            logger.warning("⚠️ البوت متوقف، محاولة إعادة التشغيل...")
                            await self.application.updater.start_polling(
                                allowed_updates=Update.ALL_TYPES,
                                drop_pending_updates=True,
                                poll_interval=1.0,
                                timeout=10,
                                bootstrap_retries=5
                            )

                        await asyncio.sleep(30)  # فحص كل 30 ثانية
                    except Exception as monitor_error:
                        logger.error(f"خطأ في مراقبة البوت: {str(monitor_error)}")
                        await asyncio.sleep(60)  # انتظار أطول في حالة الخطأ

            # بدء مراقبة البوت في الخلفية
            monitor_task = asyncio.create_task(monitor_bot_health())

            # استخدام idle() للانتظار
            await self.application.updater.idle()

        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل البوت المستمر: {str(e)}")
            # في حالة فشل idle()، نستخدم حلقة while كبديل
            try:
                while self._running:
                    await asyncio.sleep(1)
            except Exception as fallback_error:
                logger.error(f"❌ خطأ في الحلقة البديلة: {str(fallback_error)}")
        finally:
            # إيقاف مهمة المراقبة
            if 'monitor_task' in locals() and not monitor_task.done():
                monitor_task.cancel()
            await self.stop()

    async def _check_settings_periodically(self, context: CallbackContext):
        """التحقق من الإعدادات بشكل دوري"""
        try:
            logger.info("🔄 جاري التحقق من الإعدادات بشكل دوري...")

            # التحقق من الإعدادات العامة
            general_settings = system_settings.get_all()
            for key, value in general_settings.items():
                # التحقق مما إذا كان الإعداد يجب أن يكون حساسًا
                if any(keyword in key for keyword in ['API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH']):
                    logger.info(f"⚠️ تم العثور على إعداد حساس في الوثيقة العامة: {key}")
                    # نقل الإعداد إلى الوثيقة الحساسة
                    system_settings.set(key, value, sensitive=True)
                    system_settings.delete(key)
                    logger.info(f"✅ تم نقل الإعداد {key} إلى الوثيقة الحساسة")

            # التحقق من الإعدادات الحساسة
            sensitive_settings = system_settings.get_all(sensitive=True)
            for key, value in sensitive_settings.items():
                # التحقق مما إذا كان الإعداد يجب أن يكون عامًا
                if not any(keyword in key for keyword in ['API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH']):
                    logger.info(f"⚠️ تم العثور على إعداد عام في الوثيقة الحساسة: {key}")
                    # نقل الإعداد إلى الوثيقة العامة
                    system_settings.set(key, value, sensitive=False)
                    system_settings.delete(key, sensitive=True)
                    logger.info(f"✅ تم نقل الإعداد {key} إلى الوثيقة العامة")

            logger.info("✅ تم التحقق من الإعدادات بنجاح")
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من الإعدادات: {str(e)}")

    async def stop(self):
        """إيقاف البوت بشكل آمن"""
        if self._running:
            try:
                logger.info("🛑 جاري إيقاف البوت...")
                self._running = False
                if self.application.updater.running:
                    await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
                logger.info("✅ تم إيقاف البوت بنجاح")
            except Exception as e:
                logger.error(f"❌ خطأ في إيقاف البوت: {str(e)}")

    async def _handle_enhanced_callbacks(self, update: Update, context: CallbackContext):
        """معالجة أحداث النظام المحسن"""
        try:
            callback_data = update.callback_query.data

            if callback_data.startswith('compare_styles_'):
                symbol = callback_data.replace('compare_styles_', '')
                await compare_trading_styles(update, context, symbol)

            elif callback_data.startswith('set_trading_style_'):
                style = callback_data.replace('set_trading_style_', '')
                await self._set_user_trading_style(update, context, style)

            elif callback_data.startswith('refresh_analysis_'):
                symbol = callback_data.replace('refresh_analysis_', '')
                await refresh_analysis(update, context, symbol)

        except Exception as e:
            logger.error(f"خطأ في معالجة أحداث النظام المحسن: {str(e)}")
            await update.callback_query.answer(
                "❌ حدث خطأ" if context.bot_data.get('user_settings', {}).get(str(update.effective_user.id), {}).get('language', 'ar') == 'ar' else "❌ An error occurred",
                show_alert=True
            )

    async def _set_user_trading_style(self, update: Update, context: CallbackContext, style: str):
        """تعيين نمط التداول المفضل للمستخدم"""
        try:
            user_id = str(update.effective_user.id)
            subscription_system = SubscriptionSystem.get_instance()
            settings = subscription_system.get_user_settings(user_id)
            lang = settings.get('lang', 'ar')

            # تحديث نمط التداول في إعدادات المستخدم
            settings['trading_style'] = style
            subscription_system.update_user_settings(user_id, settings)

            styles_ar = {
                'scalping': 'المضاربة السريعة',
                'day_trading': 'التداول اليومي',
                'swing_trading': 'التداول المتأرجح',
                'position': 'الاستثمار طويل المدى'
            }

            style_name = styles_ar.get(style, style)

            await update.callback_query.answer(
                f"✅ تم تعيين نمط التحليل إلى: {style_name}" if lang == 'ar' else f"✅ Analysis style set to: {style}",
                show_alert=True
            )

            # العودة إلى القائمة الرئيسية
            from handlers.main_handlers import show_main_menu
            await show_main_menu(update, context)

        except Exception as e:
            logger.error(f"خطأ في تعيين نمط التحليل: {str(e)}")
            await update.callback_query.answer(
                "❌ حدث خطأ في تعيين نمط التحليل" if lang == 'ar' else "❌ Error setting analysis style",
                show_alert=True
            )
